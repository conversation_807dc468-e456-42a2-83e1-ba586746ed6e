["tests/test_core.py::TestJSONHandler::test_clear", "tests/test_core.py::TestJSONHandler::test_delete", "tests/test_core.py::TestJSONHandler::test_get_and_set", "tests/test_core.py::TestJSONHandler::test_init_with_existing_file", "tests/test_core.py::TestJSONHandler::test_init_with_new_file", "tests/test_core.py::TestJSONHandler::test_invalid_json_file", "tests/test_core.py::TestJSONHandler::test_keys_values_items", "tests/test_core.py::TestJSONHandler::test_magic_methods", "tests/test_core.py::TestJSONHandler::test_pretty_print", "tests/test_core.py::TestJSONHandler::test_to_dict", "tests/test_core.py::TestJSONHandler::test_update", "tests/test_core.py::TestJSONHandler::test_validate", "tests/test_parser.py::TestJSONParser::test_deep_merge_objects", "tests/test_parser.py::TestJSONParser::test_extract_keys", "tests/test_parser.py::TestJSONParser::test_extract_keys_not_dict", "tests/test_parser.py::TestJSONParser::test_flatten_object_custom_separator", "tests/test_parser.py::TestJSONParser::test_flatten_object_simple", "tests/test_parser.py::TestJSONParser::test_flatten_object_with_arrays", "tests/test_parser.py::TestJSONParser::test_flatten_unflatten_roundtrip", "tests/test_parser.py::TestJSONParser::test_merge_objects", "tests/test_parser.py::TestJSONParser::test_merge_objects_overlapping", "tests/test_parser.py::TestJSONParser::test_parse_file_not_exists", "tests/test_parser.py::TestJSONParser::test_parse_file_valid", "tests/test_parser.py::TestJSONParser::test_parse_string_invalid", "tests/test_parser.py::TestJSONParser::test_parse_string_valid", "tests/test_parser.py::TestJSONParser::test_to_file", "tests/test_parser.py::TestJSONParser::test_to_string_compact", "tests/test_parser.py::TestJSONParser::test_to_string_pretty", "tests/test_parser.py::TestJSONParser::test_to_string_sorted", "tests/test_parser.py::TestJSONParser::test_unflatten_object_simple", "tests/test_parser.py::TestJSONParser::test_unflatten_object_with_arrays", "tests/test_validator.py::TestJSONValidator::test_check_required_keys_missing", "tests/test_validator.py::TestJSONValidator::test_check_required_keys_not_dict", "tests/test_validator.py::TestJSONValidator::test_check_required_keys_valid", "tests/test_validator.py::TestJSONValidator::test_validate_json_file_invalid", "tests/test_validator.py::TestJSONValidator::test_validate_json_file_not_exists", "tests/test_validator.py::TestJSONValidator::test_validate_json_file_valid", "tests/test_validator.py::TestJSONValidator::test_validate_json_string_invalid", "tests/test_validator.py::TestJSONValidator::test_validate_json_string_valid", "tests/test_validator.py::TestJSONValidator::test_validate_json_types_invalid", "tests/test_validator.py::TestJSONValidator::test_validate_json_types_non_string_keys", "tests/test_validator.py::TestJSONValidator::test_validate_json_types_valid", "tests/test_validator.py::TestJSONValidator::test_validate_schema_invalid", "tests/test_validator.py::TestJSONValidator::test_validate_schema_missing_keys", "tests/test_validator.py::TestJSONValidator::test_validate_schema_nested", "tests/test_validator.py::TestJSONValidator::test_validate_schema_valid"]
# JSONUtilKit

A lightweight Python toolkit for modifying, reading, and validating J<PERSON><PERSON> (JavaScript Object Notation).

## Features

- Easy JSON file manipulation
- JSON validation
- Pretty printing
- Simple API for common JSON operations

## Installation

```bash
pip install jsonutilkit
```

## Quick Start

```python
from jsonutilkit import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

# Create a JSON handler
handler = <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("data.json")

# Get a value
value = handler.get("key")

# Set a value
handler.set("key", "value")

# Validate JSON
is_valid = handler.validate()
```

## Development

### Setup

```bash
git clone https://github.com/yourusername/jsonutilkit.git
cd jsonutilkit
pip install -e ".[dev]"
```

### Running Tests

```bash
pytest
```

### Code Formatting

```bash
black src/
isort src/
```

## License

MIT License - see LICENSE file for details.

# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Initial release of JSONUtilKit
- JSONHandler class for file-based JSON operations
- JSONValidator class for JSON validation
- JSONParser class for JSON parsing and manipulation
- Comprehensive test suite
- Professional package structure

### Changed
- Restructured codebase to follow Python packaging best practices

### Deprecated
- None

### Removed
- None

### Fixed
- None

### Security
- None

## [0.1.0] - 2024-01-01

### Added
- Initial project setup
- Basic JSON manipulation functionality

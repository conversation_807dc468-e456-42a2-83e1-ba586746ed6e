import json
import pprint

class mjson:
    def __init__(self, file):
        self.file = file
        try:
            self.data = open(self.file, 'r')
        except:
            self.data = {}
            raise Exception("File not found")
        self.keys = list(self.data.keys())

    def get(self, key):
        return self.data[key]

    def set(self, key, value):
        self.data[key] = value
        self.save()

    def save(self):
        json.dump(self.data, open(self.file, 'w'))

    def delete(self, key):
        del self.data[key]
        self.save()

    def clear(self):
        self.data = {}
        self.save()

    def validate(self):
        try:
            json.loads(self.data)
            return True
        except:
            return False

    def prettyprint(self):
        pprint.pprint(self)


    def __str__(self):
        return str(self.data)
    

j = mjson("test.json")
print(j.get("name"))
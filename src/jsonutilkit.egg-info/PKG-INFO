Metadata-Version: 2.4
Name: jsonutilkit
Version: 0.1.0
Summary: A lightweight Python toolkit for modifying, reading, and validating JSON
Home-page: https://github.com/yourusername/jsonutilkit
Author: Your Name
Author-email: Your Name <<EMAIL>>
Maintainer-email: Your Name <<EMAIL>>
License: MIT
Project-URL: Homepage, https://github.com/yourusername/jsonutilkit
Project-URL: Repository, https://github.com/yourusername/jsonutilkit.git
Project-URL: Documentation, https://jsonutilkit.readthedocs.io
Project-URL: Bug Tracker, https://github.com/yourusername/jsonutilkit/issues
Keywords: json,utility,toolkit,validation,parsing
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Utilities
Requires-Python: >=3.8
Description-Content-Type: text/markdown
License-File: LICENSE
Provides-Extra: dev
Requires-Dist: pytest>=7.0; extra == "dev"
Requires-Dist: pytest-cov>=4.0; extra == "dev"
Requires-Dist: black>=23.0; extra == "dev"
Requires-Dist: isort>=5.0; extra == "dev"
Requires-Dist: flake8>=6.0; extra == "dev"
Requires-Dist: mypy>=1.0; extra == "dev"
Requires-Dist: pre-commit>=3.0; extra == "dev"
Provides-Extra: docs
Requires-Dist: sphinx>=5.0; extra == "docs"
Requires-Dist: sphinx-rtd-theme>=1.0; extra == "docs"
Dynamic: license-file

# JSONUtilKit

A lightweight Python toolkit for modifying, reading, and validating JSON (JavaScript Object Notation).

## Features

- Easy JSON file manipulation
- JSON validation
- Pretty printing
- Simple API for common JSON operations

## Installation

```bash
pip install jsonutilkit
```

## Quick Start

```python
from jsonutilkit import JSONHandler

# Create a JSON handler
handler = JSONHandler("data.json")

# Get a value
value = handler.get("key")

# Set a value
handler.set("key", "value")

# Validate JSON
is_valid = handler.validate()
```

## Development

### Setup

```bash
git clone https://github.com/yourusername/jsonutilkit.git
cd jsonutilkit
pip install -e ".[dev]"
```

### Running Tests

```bash
pytest
```

### Code Formatting

```bash
black src/
isort src/
```

## License

MIT License - see LICENSE file for details.

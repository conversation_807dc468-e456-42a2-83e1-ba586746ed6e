"""
JSONUtilKit - A lightweight Python toolkit for JSON manipulation.

This package provides utilities for reading, writing, validating, and manipulating JSON data.
"""

__version__ = "0.1.0"
__author__ = "Your Name"
__email__ = "<EMAIL>"

from .core import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .parser import JSONParser
from .validator import JSONValidator

__all__ = [
    "JSONHandler",
    "JSONValidator",
    "JSONParser",
]
